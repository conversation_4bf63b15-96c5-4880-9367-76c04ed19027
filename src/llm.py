import json
import os
from typing import Any, Dict

from dotenv import load_dotenv

from .database import execute_safe_query, get_dashboard_stats
from .models import NaturalQueryResponse

# Load environment variables
load_dotenv()

try:
    from litellm import acompletion

    LLM_AVAILABLE = True
except ImportError:
    LLM_AVAILABLE = False

# Database schema context for LLM
DATABASE_SCHEMA = """
Database Schema for World of Airports Game Tracker:

1. airports: Game airports
   - id (PRIMARY KEY)
   - code (VARCHAR(10)) - Airport code like 'INN', 'BRI'
   - name (VARCHAR(100)) - Full airport name
   - region (VARCHAR(50)) - Geographic region

2. plane_models: Aircraft types
   - id (PRIMARY KEY)
   - model_name (VARCHAR(50)) - e.g., 'A380', 'Boeing 747'
   - level (CHAR(1)) - Plane class A-G (A=largest, G=smallest)
   - capacity (INTEGER) - Passenger capacity
   - speed (INTEGER) - Cruise speed

3. contracts: Player-to-player plane contracts
   - id (PRIMARY KEY)
   - plane_model_id (FOREIGN KEY to plane_models)
   - recipient_player_id (VARCHAR(50)) - Receiving player name
   - airport_id (FOREIGN KEY to airports)
   - contract_date (TIMESTAMP)
   - status (VARCHAR(20)) - 'active', 'completed', 'cancelled'

4. player_activities: Player actions tracking
   - id (PRIMARY KEY)
   - player_id (VARCHAR(50)) - Player name
   - airport_id (FOREIGN KEY to airports)
   - activity_type (VARCHAR(50)) - Type of activity
   - last_active (TIMESTAMP)

Available airports: INN, BRI, IAD, SAN, LHR, PRG, PHX, NRT, MCT, KWI, NGO, LGA, SXM, BKK
Plane levels: A (largest) to G (smallest)
"""

QUERY_EXAMPLES = """
Example queries and their SQL translations:

"Show A380 contracts from last week"
→ SELECT * FROM contracts c JOIN plane_models p ON c.plane_model_id = p.id WHERE p.model_name = 'A380' AND c.contract_date >= date('now', '-7 days')

"Who are the most active players?"
→ SELECT player_id, COUNT(*) as activity_count FROM player_activities GROUP BY player_id ORDER BY activity_count DESC LIMIT 10

"Which airports have the most contracts?"
→ SELECT a.code, a.name, COUNT(*) as contract_count FROM contracts c JOIN airports a ON c.airport_id = a.id GROUP BY a.id ORDER BY contract_count DESC

"Show recent player activity at Innsbruck"
→ SELECT pa.*, a.name FROM player_activities pa JOIN airports a ON pa.airport_id = a.id WHERE a.code = 'INN' ORDER BY pa.last_active DESC LIMIT 20

"What are the most popular plane types in contracts?"
→ SELECT pm.model_name, pm.level, COUNT(*) as contract_count FROM contracts c JOIN plane_models pm ON c.plane_model_id = pm.id GROUP BY pm.id ORDER BY contract_count DESC
"""


async def process_natural_language_query(query: str) -> NaturalQueryResponse:
    """Process natural language query and return SQL results with interpretation"""

    if not LLM_AVAILABLE:
        # Fallback without LLM
        return NaturalQueryResponse(
            sql='-- LLM not available',
            results=[],
            interpretation='LLM integration not configured. Please set up OpenAI or Anthropic API keys.',
        )

    # Check for API keys
    openai_key = os.getenv('OPENAI_API_KEY')
    anthropic_key = os.getenv('ANTHROPIC_API_KEY')

    if not openai_key and not anthropic_key:
        return NaturalQueryResponse(
            sql='-- No API key configured',
            results=[],
            interpretation='Please configure OPENAI_API_KEY or ANTHROPIC_API_KEY environment variable.',
        )

    try:
        # Generate SQL using LLM
        context = f"""
You are an expert SQL assistant for a World of Airports game tracking database.

{DATABASE_SCHEMA}

{QUERY_EXAMPLES}

User Query: "{query}"

Generate a safe SELECT-only SQL query to answer this question.
Return ONLY the SQL query, no explanations or formatting.
Ensure the query uses proper JOINs when accessing related tables.
"""

        model = 'gpt-4o-mini' if openai_key else 'claude-3-haiku-20240307'

        response = await acompletion(
            model=model, messages=[{'role': 'user', 'content': context}], temperature=0.1, max_tokens=300
        )

        sql = response.choices[0].message.content.strip()

        # Clean up the SQL (remove markdown formatting if present)
        if sql.startswith('```sql'):
            sql = sql[6:]
        if sql.endswith('```'):
            sql = sql[:-3]
        sql = sql.strip()

        # Execute the query safely
        try:
            results = await execute_safe_query(sql)
        except Exception as e:
            return NaturalQueryResponse(sql=sql, results=[], interpretation=f'Query execution failed: {str(e)}')

        # Generate human-readable interpretation
        interpretation_context = f"""
The user asked: "{query}"
The SQL query was: {sql}
The results were: {json.dumps(results[:5])}{'...' if len(results) > 5 else ''}

Provide a clear, concise explanation of what these results show in the context of World of Airports game tracking.
Focus on the key insights and patterns. If there are many results, summarize the main findings.
"""

        interpretation_response = await acompletion(
            model=model, messages=[{'role': 'user', 'content': interpretation_context}], temperature=0.3, max_tokens=200
        )

        interpretation = interpretation_response.choices[0].message.content.strip()

        return NaturalQueryResponse(sql=sql, results=results, interpretation=interpretation)

    except Exception as e:
        return NaturalQueryResponse(
            sql='-- Error occurred', results=[], interpretation=f'Failed to process query: {str(e)}'
        )


async def generate_insights() -> str:
    """Generate AI-powered insights about game patterns"""

    if not LLM_AVAILABLE:
        return 'LLM integration not available. Please install litellm and configure API keys.'

    # Check for API keys
    openai_key = os.getenv('OPENAI_API_KEY')
    anthropic_key = os.getenv('ANTHROPIC_API_KEY')

    if not openai_key and not anthropic_key:
        return 'Please configure OPENAI_API_KEY or ANTHROPIC_API_KEY environment variable.'

    try:
        # Gather recent data for analysis
        dashboard_stats = await get_dashboard_stats()

        # Get recent contracts data
        recent_contracts = await execute_safe_query("""
            SELECT
                pm.model_name, pm.level, a.code as airport,
                c.status, c.recipient_player_id,
                date(c.contract_date) as date
            FROM contracts c
            JOIN plane_models pm ON c.plane_model_id = pm.id
            JOIN airports a ON c.airport_id = a.id
            WHERE c.contract_date >= date('now', '-7 days')
            ORDER BY c.contract_date DESC
            LIMIT 50
        """)

        # Get player activity patterns
        player_patterns = await execute_safe_query("""
            SELECT
                player_id,
                COUNT(*) as activity_count,
                COUNT(DISTINCT activity_type) as activity_types,
                MAX(last_active) as last_seen
            FROM player_activities
            WHERE last_active >= date('now', '-7 days')
            GROUP BY player_id
            ORDER BY activity_count DESC
            LIMIT 20
        """)

        model = 'gpt-4o-mini' if openai_key else 'claude-3-haiku-20240307'

        analysis_prompt = f"""
Analyze this World of Airports game data and provide strategic insights:

Dashboard Stats: {json.dumps(dashboard_stats)}

Recent Contracts (last 7 days): {json.dumps(recent_contracts[:20])}

Player Activity Patterns: {json.dumps(player_patterns[:10])}

Provide insights on:
1. Most profitable routes and plane combinations
2. Player engagement patterns and trends
3. Airport utilization efficiency
4. Contract completion rates and optimization opportunities
5. Recommendations for improving game strategy

Keep the analysis concise and actionable, focusing on key patterns and opportunities.
"""

        response = await acompletion(
            model=model, messages=[{'role': 'user', 'content': analysis_prompt}], temperature=0.7, max_tokens=500
        )

        return response.choices[0].message.content.strip()

    except Exception as e:
        return f'Failed to generate insights: {str(e)}'


async def process_voice_command(command: str) -> Dict[str, Any]:
    """Process voice commands and convert to structured actions"""

    if not LLM_AVAILABLE:
        return {'error': 'LLM integration not available'}

    # Check for API keys
    openai_key = os.getenv('OPENAI_API_KEY')
    anthropic_key = os.getenv('ANTHROPIC_API_KEY')

    if not openai_key and not anthropic_key:
        return {'error': 'API key not configured'}

    try:
        model = 'gpt-4o-mini' if openai_key else 'claude-3-haiku-20240307'

        command_context = f"""
Parse this voice command for World of Airports game tracking and return a structured JSON action:

Voice command: "{command}"

Available actions:
- create_contract: needs plane_model, recipient_player, airport
- create_activity: needs player_id, activity_type, airport
- query_data: needs search_query

Available airports: INN, BRI, IAD, SAN, LHR, PRG, PHX, NRT, MCT, KWI, NGO, LGA, SXM, BKK
Common plane models: A380, Boeing 747, Boeing 777, Airbus A350, Boeing 787, etc.
Activity types: landing, takeoff, contract_sent, contract_received, maintenance, etc.

Return only valid JSON with the action type and parameters. If the command is unclear, return an error.

Example outputs:
{{"action": "create_contract", "params": {{"plane_model": "A380", "recipient_player": "John", "airport": "INN"}}}}
{{"action": "query_data", "params": {{"search_query": "show recent A380 contracts"}}}}
{{"error": "Could not understand command"}}
"""

        response = await acompletion(
            model=model, messages=[{'role': 'user', 'content': command_context}], temperature=0.2, max_tokens=200
        )

        result = response.choices[0].message.content.strip()

        # Try to parse as JSON
        try:
            return json.loads(result)
        except json.JSONDecodeError:
            return {'error': 'Failed to parse command', 'raw_response': result}

    except Exception as e:
        return {'error': f'Failed to process voice command: {str(e)}'}
